<?php

// Iniciar sesión si es necesario
use App\classes\Configuracion;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en form_aliados_comerciales.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$configuraciones = []; // Initialize as an empty array
$pdf_error       = null;
$pdf_success     = null;
#endregion init variables
#region region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success
#region region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region region Modify configuration
// --- Handle AJAX Request (Modify Configuration) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'modificar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response

	$configId         = filter_input(INPUT_POST, 'configId', FILTER_VALIDATE_INT);
	$nuevaDescripcion = filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_SPECIAL_CHARS);
	$nuevoValor       = filter_input(INPUT_POST, 'valor', FILTER_SANITIZE_SPECIAL_CHARS);
	$nuevoEsEditable  = isset($_POST['es_editable']) ? 1 : 0; // Checkbox handling
	$nuevaDescripcion = trim($nuevaDescripcion ?? '');
	$nuevoValor       = trim($nuevoValor ?? '');

	if ($configId && !empty($nuevaDescripcion)) {
		try {
			// Get the existing configuration
			$configuracion = Configuracion::get($configId, $conexion);

			if ($configuracion) {
				// Check if configuration is editable before allowing updates
				if ($configuracion->getEsEditable() == 0) {
					$response['message'] = 'Error: Esta configuración no es editable.';
					http_response_code(403); // Forbidden
				} else {
					// Update the valor and es_editable, not the descripcion
					$configuracion->setValor($nuevoValor);
					$configuracion->setEsEditable($nuevoEsEditable);
					$success = $configuracion->guardar($conexion);

					if ($success) {
						$response['success'] = true;
						$response['message'] = 'Configuración actualizada correctamente.';
					} else {
						$response['message'] = 'Error: No se pudo actualizar la configuración.';
						http_response_code(500); // Internal Server Error
					}
				}
			} else {
				$response['message'] = 'Error: No se pudo encontrar la configuración.';
				http_response_code(404); // Not Found
			}
		} catch (Exception $e) {
			$response['message'] = "Error al modificar configuración: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else if (!$configId) {
		$response['message'] = 'Error: ID de configuración inválido.';
		http_response_code(400); // Bad Request
	} else {
		$response['message'] = 'Error: La descripción no puede estar vacía.';
		http_response_code(400); // Bad Request
	}

	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Modify configuration

#region region Handle PDF Upload
// --- Handle PDF Upload ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'upload_pdf') {
	// Check if file was uploaded without errors
	if (isset($_FILES['pdf_file']) && $_FILES['pdf_file']['error'] == 0) {
		$file = $_FILES['pdf_file'];

		// Validate file type
		$file_type = $file['type'];
		if ($file_type !== 'application/pdf') {
			$pdf_error = "Error: Solo se permiten archivos PDF. Tipo detectado: {$file_type}";
		} // Validate file size (max 10MB)
		elseif ($file['size'] > 30 * 1024 * 1024) {
			$pdf_error = "Error: El archivo es demasiado grande. El tamaño máximo permitido es 10MB.";
		} else {
			// Create upload directory if it doesn't exist
			$upload_dir = Configuracion::URL_CARPETA_PDF_NEGOCIOS;
			if (!file_exists($upload_dir)) {
				if (!mkdir($upload_dir, 0755, true)) {
					$pdf_error = "Error: No se pudo crear el directorio de destino para el archivo.";
					return;
				}
			}

			// Ensure directory is writable
			if (!is_writable($upload_dir)) {
				$pdf_error = "Error: El directorio de destino no tiene permisos de escritura.";
				return;
			}

			// Generate filename with timestamp
			$timestamp   = date('YmdHis'); // Format: yyyyMMddhhmmss
			$filename    = "pdf_plan_negocios_{$timestamp}.pdf";
			$target_file = $upload_dir . $filename;

			// Move uploaded file to target location
			if (move_uploaded_file($file['tmp_name'], $target_file)) {
				// Update or create configuration entry
				try {
					// Check if configuration already exists
					$configuracion = Configuracion::get_by_descripcion('PDF Plan de negocios', $conexion);

					if (!$configuracion) {
						// Create new configuration
						$configuracion = new Configuracion();
						$configuracion->setDescripcion('PDF Plan de negocios');
					}

					// Update configuration value with filename
					$configuracion->setValor($filename);
					$success = $configuracion->guardar($conexion);

					if ($success) {
						$pdf_success = "El archivo PDF ha sido subido correctamente como '{$filename}'.";
					} else {
						$pdf_error = "Error: El archivo se subió pero no se pudo actualizar la configuración.";
					}
				} catch (Exception $e) {
					$pdf_error = "Error al guardar la configuración: " . $e->getMessage();
				}
			} else {
				$pdf_error = "Error: No se pudo mover el archivo subido a la ubicación final.";
			}
		}
	} else {
		$error_code = $_FILES['pdf_file']['error'] ?? 'desconocido';
		$pdf_error  = "Error al subir el archivo (código {$error_code}). Por favor, inténtelo de nuevo.";
	}
}
#endregion Handle PDF Upload

#region region Handle POST Actions (Delete)
// --- Handle Non-AJAX POST Action (Delete Configuration) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'eliminar') {
	$configIdToDelete = filter_input(INPUT_POST, 'configId', FILTER_VALIDATE_INT);

	if ($configIdToDelete) {
		try {
			$success = Configuracion::eliminar($configIdToDelete, $conexion);

			if ($success) {
				$_SESSION['flash_message_success'] = "Configuración eliminada correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o eliminar la configuración.";
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al eliminar configuración: " . $e->getMessage();
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de configuración inválido para eliminar.";
	}

	// Redirect back to the configuration list page after processing
	header('Location: lconfiguraciones');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	$parametros      = [];
	$configuraciones = Configuracion::get_list($parametros, $conexion);

	// Get current PDF Plan de negocios configuration if it exists
	$pdf_config           = Configuracion::get_by_descripcion('PDF Plan de negocios', $conexion);
	$current_pdf_filename = $pdf_config ? $pdf_config->getValor() : null;

} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de configuraciones.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de configuraciones: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/admin/lconfiguraciones.view.php';

?>
