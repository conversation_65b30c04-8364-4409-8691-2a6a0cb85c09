<?php
#region region DOCS

/** @var Configuracion[] $configuraciones */

use App\classes\Configuracion;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Configuraciones</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Configuraciones</h4>
				<p class="mb-0 text-muted">Administra las configuraciones del sistema</p>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region PANEL PDF UPLOAD ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Plan de Negocios PDF
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-3">
				<form id="pdf-upload-form" method="POST" action="lconfiguraciones" enctype="multipart/form-data">
					<input type="hidden" name="action" value="upload_pdf">
					<div class="mb-3">
						<label for="pdf_file" class="form-label">Subir Plan de Negocios (PDF)</label>
						<input type="file" class="form-control" id="pdf_file" name="pdf_file" accept="application/pdf" required>
						<div class="form-text">Solo se permiten archivos PDF. El archivo se guardará como "pdf_plan_negocios_{fecha}.pdf"</div>
					</div>
					<div class="mb-3">
						<button type="submit" class="btn btn-primary">Subir PDF</button>
					</div>
					<?php if (!empty($current_pdf_filename)): ?>
						<div class="mb-3">
							<div class="alert alert-info">
								<strong>PDF Actual:</strong> <?php echo htmlspecialchars($current_pdf_filename); ?>
								<a href="<?php echo Configuracion::URL_WEB_CARPETA_PDF_NEGOCIOS . htmlspecialchars($current_pdf_filename); ?>" download="<?php echo htmlspecialchars($current_pdf_filename); ?>" class="btn btn-sm btn-info ms-2">
									<i class="fa fa-download"></i> Descargar PDF
								</a>
							</div>
						</div>
					<?php endif; ?>
					<?php if (!empty($pdf_error)): ?>
						<div class="alert alert-danger"><?php echo $pdf_error; ?></div>
					<?php endif; ?>
					<?php if (!empty($pdf_success)): ?>
						<div class="alert alert-success"><?php echo $pdf_success; ?></div>
					<?php endif; ?>
				</form>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL PDF UPLOAD ?>

		<?php #region region PANEL CONFIGURACIONES ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Configuraciones Activas
				</h4>
				<div class="ms-auto">
					<a href="iconfiguracion" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nueva</a>
				</div>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE CONFIGURACIONES ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th>Acciones</th>
						<th>Descripción</th>
						<th>Valor</th>
						<th>Editable</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="config-table-body">
					<?php foreach ($configuraciones as $configuracion): ?>
						<tr data-config-id="<?php echo $configuracion->getId(); ?>">
							<td>
								<?php // Edit Button - Triggers Modal ?>
								<?php if ($configuracion->getEsEditable() == 1): ?>
									<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-config"
									        title="Editar Configuración"
									        data-bs-toggle="modal"
									        data-bs-target="#editConfigModal"
									        data-configid="<?php echo $configuracion->getId(); ?>"
									        data-descripcion="<?php echo htmlspecialchars($configuracion->getDescripcion() ?? ''); ?>"
									        data-valor="<?php echo htmlspecialchars($configuracion->getValor() ?? ''); ?>"
									        data-es-editable="<?php echo $configuracion->getEsEditable(); ?>">
										<i class="fa fa-edit"></i>
									</button>
								<?php else: ?>
									<button type="button" class="btn btn-xs btn-secondary me-1"
									        title="Esta configuración no es editable" disabled>
										<i class="fa fa-edit"></i>
									</button>
								<?php endif; ?>
								<?php // Delete Button ?>
								<button type="button" class="btn btn-xs btn-danger btn-eliminar-config"
								        title="Eliminar"
								        data-configid="<?php echo $configuracion->getId(); ?>"
								        data-descripcion="<?php echo htmlspecialchars($configuracion->getDescripcion() ?? ''); ?>">
									<i class="fa fa-trash-alt"></i>
								</button>
							</td>
							<td class="config-descripcion-cell"><?php echo htmlspecialchars($configuracion->getDescripcion()); ?></td>
							<td class="config-valor-cell"><?php echo htmlspecialchars($configuracion->getValor()); ?></td>
							<td class="config-editable-cell">
								<?php if ($configuracion->getEsEditable() == 1): ?>
									<span class="badge bg-success">Sí</span>
								<?php else: ?>
									<span class="badge bg-secondary">No</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($configuraciones)): ?>
						<tr>
							<td colspan="5" class="text-center">No hay configuraciones para mostrar.</td>
						</tr>
					<?php endif; ?>

					</tbody>
				</table>
				<?php #endregion TABLE CONFIGURACIONES ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL CONFIGURACIONES ?>
		<?php #region region Edit Config Modal ?>
		<div class="modal fade" id="editConfigModal" tabindex="-1" aria-labelledby="editConfigModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="edit-config-form">
						<div class="modal-header">
							<h5 class="modal-title" id="editConfigModalLabel">Editar Configuración</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="configId" id="edit-config-id">
							<input type="hidden" name="action" value="modificar">

							<div class="mb-3">
								<label for="edit-config-descripcion" class="form-label">Descripción:</label>
								<input type="text" class="form-control fs-14px" id="edit-config-descripcion" name="descripcion" readonly>
							</div>
							<div class="mb-3">
								<label for="edit-config-valor" class="form-label">Valor:</label>
								<input type="text" class="form-control" id="edit-config-valor" name="valor">
							</div>
							<div class="mb-3">
								<div class="form-check">
									<input type="checkbox" class="form-check-input" id="edit-config-es-editable" name="es_editable" value="1">
									<label class="form-check-label" for="edit-config-es-editable">
										Es editable
									</label>
								</div>
							</div>
							<div id="edit-config-error" class="alert alert-danger mt-2" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar Cambios</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Edit Config Modal ?>

	</div>
	<!-- END #content -->

	<?php #region region Hidden Form for Deletion ?>
	<form id="delete-config-form" method="POST" action="lconfiguraciones" style="display: none;">
		<input type="hidden" name="action" value="eliminar">
		<input type="hidden" name="configId" id="delete-config-id">
	</form>
	<?php #endregion Hidden Form ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<!-- Assuming SweetAlert is loaded in core_js or globally -->
<!-- Assuming SweetAlert helper functions (showSweetAlertSuccess/Error) are available -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // PDF Upload Form Validation
        const pdfUploadForm = document.getElementById('pdf-upload-form');
        const pdfFileInput  = document.getElementById('pdf_file');

        if (pdfUploadForm) {
            pdfUploadForm.addEventListener('submit', function (event) {
                // Reset any previous validation styling
                pdfFileInput.classList.remove('is-invalid');

                // Check if a file is selected
                if (!pdfFileInput.files || pdfFileInput.files.length === 0) {
                    event.preventDefault();
                    pdfFileInput.classList.add('is-invalid');
                    return false;
                }

                const file = pdfFileInput.files[0];

                // Validate file type
                if (file.type !== 'application/pdf') {
                    event.preventDefault();
                    pdfFileInput.classList.add('is-invalid');
                    showSweetAlertError('Error', 'Solo se permiten archivos PDF. Por favor, seleccione un archivo PDF válido.');
                    return false;
                }

                // Validate file size (max 10MB)
                const maxSize = 30 * 1024 * 1024; // 10MB in bytes
                if (file.size > maxSize) {
                    event.preventDefault();
                    pdfFileInput.classList.add('is-invalid');
                    showSweetAlertError('Error', 'El archivo es demasiado grande. El tamaño máximo permitido es 10MB.');
                    return false;
                }

                // If all validations pass, show loading indicator
                document.getElementById('loader').style.display = 'block';
                return true;
            });
        }
        // Use event delegation on the table body
        const tableBody                  = document.getElementById('config-table-body');
        const editConfigModalElement     = document.getElementById('editConfigModal');
        const editConfigModal            = new bootstrap.Modal(editConfigModalElement); // Initialize Bootstrap Modal
        const editConfigForm             = document.getElementById('edit-config-form');
        const editConfigIdInput          = document.getElementById('edit-config-id');
        const editConfigDescripcionInput = document.getElementById('edit-config-descripcion');
        const editConfigValorInput       = document.getElementById('edit-config-valor');
        const editConfigEsEditableInput  = document.getElementById('edit-config-es-editable');
        const editConfigErrorDiv         = document.getElementById('edit-config-error');

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deleteButton = event.target.closest('.btn-eliminar-config');
                const editButton   = event.target.closest('.btn-edit-config');

                // --- Handle Delete Click ---
				<?php #region region JS AJAX -- Delete config ?>
                if (deleteButton) {
                    event.preventDefault();
                    const configId          = deleteButton.dataset.configid;
                    const configDescripcion = deleteButton.dataset.descripcion || 'esta configuración';

                    swal({
                        title     : "Confirmar Eliminación",
                        text      : `¿Seguro que quieres eliminar la configuración '${configDescripcion}'?`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    })
                        .then((willDelete) => {
                            if (willDelete) {
                                document.getElementById('delete-config-id').value = configId;
                                document.getElementById('delete-config-form').submit();
                            }
                        });
                }
				<?php #endregion JS AJAX -- Delete config ?>

                // --- Handle Edit Click ---
				<?php #region region JS AJAX - Handle Edit click ?>
                if (editButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const configId           = editButton.dataset.configid;
                    const currentDescripcion = editButton.dataset.descripcion;
                    const currentValor       = editButton.dataset.valor;
                    const currentEsEditable  = editButton.dataset.esEditable;

                    // Populate the modal form
                    editConfigIdInput.value          = configId;
                    editConfigDescripcionInput.value = currentDescripcion;
                    editConfigValorInput.value       = currentValor;
                    editConfigEsEditableInput.checked = (currentEsEditable == '1');
                    editConfigErrorDiv.style.display = 'none'; // Hide previous errors
                    editConfigErrorDiv.textContent   = '';

                    // The data-bs-toggle and data-bs-target attributes on the button handle showing the modal
                    // If they weren't there, you'd call: editConfigModal.show();
                }
				<?php #endregion JS AJAX - Edit config ?>
            });
        }

        // --- Handle Edit Form Submission (AJAX) ---
		<?php #region region JS AJAX - Edit Form Submission ?>
        if (editConfigForm) {
            editConfigForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                editConfigErrorDiv.style.display = 'none'; // Hide error div initially

                const formData         = new FormData(editConfigForm);
                const configId         = formData.get('configId');
                const nuevaDescripcion = formData.get('descripcion').trim(); // Get trimmed description
                const nuevoValor       = formData.get('valor').trim(); // Get trimmed value
                const nuevoEsEditable  = formData.get('es_editable') ? '1' : '0'; // Checkbox handling

                // No need to validate descripcion as it's read-only and not updated

                // Disable submit button during request
                const submitButton    = editConfigForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('lconfiguraciones', { // Post to the same controller page
                    method: 'POST',
                    body  : formData // FormData handles content type automatically
                })
                    .then(response => {
                        // Check if response is ok (status 200-299) AND is JSON
                        if (!response.ok) {
                            // Try to parse error from JSON, otherwise use status text
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                // If response wasn't JSON or parsing failed
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json(); // Parse JSON body
                    })
                    .then(data => {
                        if (data.success) {
                            editConfigModal.hide(); // Close modal on success

                            // Update the values in the table row directly
                            const tableRow = document.querySelector(`#config-table-body tr[data-config-id="${configId}"]`);
                            if (tableRow) {
                                // Find the cells with the specific classes within that row
                                const descripcionCell = tableRow.querySelector('.config-descripcion-cell');
                                const valorCell       = tableRow.querySelector('.config-valor-cell');
                                const editableCell    = tableRow.querySelector('.config-editable-cell');

                                // Don't update descripcion as it's not changed

                                if (valorCell) {
                                    valorCell.textContent = nuevoValor; // Update cell text
                                }

                                if (editableCell) {
                                    // Update editable status display
                                    if (nuevoEsEditable == '1') {
                                        editableCell.innerHTML = '<span class="badge bg-success">Sí</span>';
                                    } else {
                                        editableCell.innerHTML = '<span class="badge bg-secondary">No</span>';
                                    }
                                }

                                // Also update the data attributes on the edit and delete buttons for next time
                                const editButton = tableRow.querySelector('.btn-edit-config');
                                if (editButton) {
                                    // Don't update descripcion in dataset
                                    editButton.dataset.valor = nuevoValor;
                                    editButton.dataset.esEditable = nuevoEsEditable;
                                }

                                // No need to update delete button's dataset as descripcion doesn't change
                            }

                            showSweetAlertSuccess('Éxito', 'Configuración actualizada correctamente.');

                        } else {
                            // Show error message inside the modal
                            editConfigErrorDiv.textContent   = data.message || 'Ocurrió un error al guardar.';
                            editConfigErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error updating configuration:', error);
                        // Show error message inside the modal
                        editConfigErrorDiv.textContent   = 'Error de red o del servidor: ' + error.message;
                        editConfigErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }
		<?php #endregion JS AJAX - Edit Form Submission ?>
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
